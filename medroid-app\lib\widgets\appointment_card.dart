import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/screens/main_video_consultation_screen.dart';
import 'package:medroid_app/screens/reschedule_appointment_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/payment_service.dart';
import 'package:medroid_app/utils/app_colors.dart';

class AppointmentCard extends StatefulWidget {
  final Appointment appointment;
  final Function onActionCompleted;
  final bool isProcessingPayment;

  const AppointmentCard({
    Key? key,
    required this.appointment,
    required this.onActionCompleted,
    this.isProcessingPayment = false,
  }) : super(key: key);

  @override
  AppointmentCardState createState() => AppointmentCardState();
}

class AppointmentCardState extends State<AppointmentCard> {
  bool _isProcessingAction = false;
  late PaymentService _paymentService;

  @override
  void initState() {
    super.initState();
    _paymentService = PaymentService();
  }

  void _joinVideoConsultation() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MainVideoConsultationScreen(
          appointmentId: widget.appointment.id.toString(),
        ),
      ),
    );
  }

  Future<void> _rescheduleAppointment() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RescheduleAppointmentScreen(
          appointment: widget.appointment,
        ),
      ),
    );

    if (result == true) {
      // Refresh appointments if rescheduled successfully
      widget.onActionCompleted();
    }
  }

  Future<void> _cancelAppointment() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Appointment'),
        content:
            const Text('Are you sure you want to cancel this appointment?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isProcessingAction = true;
    });

    try {
      if (!mounted) return;
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Update status to cancelled
      await apiService.updateAppointment(
        appointmentId: widget.appointment.id.toString(),
        status: 'cancelled',
      );

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Appointment cancelled successfully'),
          behavior: SnackBarBehavior.floating,
        ),
      );

      // Refresh appointments
      widget.onActionCompleted();
    } catch (e) {
      debugPrint('Error cancelling appointment: $e');

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error cancelling appointment: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingAction = false;
        });
      }
    }
  }

  void _showReasonDialog() {
    final isDesktop = MediaQuery.of(context).size.width > 768;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: isDesktop ? 600 : MediaQuery.of(context).size.width * 0.9,
            maxHeight:
                isDesktop ? 500 : MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(isDesktop ? 16 : 20),
                decoration: BoxDecoration(
                  color: AppColors.tealSurge.withAlpha(26), // 10% opacity
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.description_outlined,
                      color: AppColors.tealSurge,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Appointment Reason',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.tealSurge,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, size: 18),
                      color: AppColors.slateGrey,
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(isDesktop ? 16 : 20),
                  child: _buildFormattedReferralNote(
                      widget.appointment.reason, isDesktop),
                ),
              ),

              // Footer
              Container(
                padding: EdgeInsets.all(isDesktop ? 16 : 20),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.coralPop,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        vertical: isDesktop ? 10 : 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Close',
                      style: TextStyle(
                        fontSize: isDesktop ? 14 : 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormattedReferralNote(String content, bool isDesktop) {
    // Parse the referral note content and format it nicely
    final lines = content.split('\n');
    List<Widget> widgets = [];

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      if (line.isEmpty) continue;

      // Check for different types of content and style accordingly
      if (line.startsWith('REFERRAL NOTE') || line.contains('AI Doctor')) {
        // Main title
        widgets.add(
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.tealSurge.withAlpha(26), // 10% opacity
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.tealSurge.withAlpha(51), // 20% opacity
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.medical_information,
                  color: AppColors.tealSurge,
                  size: isDesktop ? 18 : 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    line,
                    style: TextStyle(
                      fontSize: isDesktop ? 14 : 15,
                      fontWeight: FontWeight.bold,
                      color: AppColors.tealSurge,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (line.startsWith('Date:') || line.startsWith('Patient ID:')) {
        // Metadata
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  line.startsWith('Date:')
                      ? Icons.calendar_today
                      : Icons.person,
                  color: AppColors.slateGrey,
                  size: 14,
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    line,
                    style: TextStyle(
                      fontSize: isDesktop ? 12 : 13,
                      color: AppColors.slateGrey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (line.endsWith(':') && line.length < 50) {
        // Section headers
        widgets.add(
          Container(
            margin: const EdgeInsets.only(top: 16, bottom: 8),
            child: Row(
              children: [
                Container(
                  width: 3,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.coralPop,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    line,
                    style: TextStyle(
                      fontSize: isDesktop ? 13 : 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimaryLight,
                      letterSpacing: 0.3,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (line.startsWith('*') ||
          line.startsWith('-') ||
          RegExp(r'^\d+\.').hasMatch(line)) {
        // Bullet points or numbered lists
        final cleanLine = line.replaceFirst(RegExp(r'^[\*\-\d+\.\s]+'), '');
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(left: 16, bottom: 6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 4,
                  height: 4,
                  decoration: const BoxDecoration(
                    color: AppColors.coralPop,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    cleanLine,
                    style: TextStyle(
                      fontSize: isDesktop ? 12 : 13,
                      color: AppColors.textPrimaryLight,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (line.contains('URGENCY') || line.contains('RECOMMENDED')) {
        // Important information
        widgets.add(
          Container(
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: AppColors.coralPop.withAlpha(26), // 10% opacity
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: AppColors.coralPop.withAlpha(51), // 20% opacity
                width: 1,
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.priority_high,
                  color: AppColors.coralPop,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    line,
                    style: TextStyle(
                      fontSize: isDesktop ? 12 : 13,
                      color: AppColors.textPrimaryLight,
                      fontWeight: FontWeight.w600,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        // Regular content
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              line,
              style: TextStyle(
                fontSize: isDesktop ? 12 : 13,
                color: AppColors.textPrimaryLight,
                height: 1.5,
              ),
            ),
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Future<void> _deleteAppointment() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Appointment'),
        content: const Text(
          'Are you sure you want to delete this appointment? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isProcessingAction = true;
    });

    try {
      if (!mounted) return;
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Delete the appointment
      final success = await apiService.deleteAppointment(
        widget.appointment.id.toString(),
      );

      if (!mounted) return;
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment deleted successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Refresh appointments
        widget.onActionCompleted();
      } else {
        throw Exception('Failed to delete appointment');
      }
    } catch (e) {
      debugPrint('Error deleting appointment: $e');

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting appointment: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingAction = false;
        });
      }
    }
  }

  Future<void> _processPayment() async {
    if (widget.appointment.amount == null || widget.appointment.amount! <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid payment amount'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isProcessingAction = true;
    });

    try {
      // First, check if the slot is still available
      final apiService = RepositoryProvider.of<ApiService>(context);
      final isAvailable = await apiService.checkSlotAvailability(
        providerId: widget.appointment.providerId.toString(),
        date: DateFormat('yyyy-MM-dd').format(widget.appointment.date),
        timeSlot: widget.appointment.timeSlot,
      );

      if (!mounted) return;

      if (!isAvailable) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'This time slot is no longer available. Please choose another time.'),
            behavior: SnackBarBehavior.floating,
          ),
        );

        setState(() {
          _isProcessingAction = false;
        });

        // Refresh appointments to show updated status
        widget.onActionCompleted();
        return;
      }

      // Create payment intent
      final paymentIntentResponse = await _paymentService.createPaymentIntent(
        appointmentId: widget.appointment.id.toString(),
        amount: widget.appointment.amount!,
        currency: 'usd',
      );

      if (!mounted) return;

      final paymentIntentId = paymentIntentResponse['id'];
      final clientSecret = paymentIntentResponse['client_secret'];

      // Process payment with Stripe
      final paymentResult = await _paymentService.processPayment(
        paymentIntentId: paymentIntentId,
        paymentIntentClientSecret: clientSecret,
      );

      if (!mounted) return;

      if (paymentResult['success'] == true) {
        // Confirm payment on the server
        final confirmResult = await _paymentService.confirmPayment(
          paymentIntentId: paymentIntentId,
          appointmentId: widget.appointment.id.toString(),
        );

        if (!mounted) return;

        if (confirmResult['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment successful! Appointment confirmed.'),
              behavior: SnackBarBehavior.floating,
            ),
          );

          // Refresh appointments
          widget.onActionCompleted();
        } else {
          throw Exception(
              'Payment confirmation failed: ${confirmResult['message']}');
        }
      } else {
        throw Exception('Payment processing failed: ${paymentResult['error']}');
      }
    } catch (e) {
      debugPrint('Error processing payment: $e');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error processing payment: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingAction = false;
        });
      }
    }
  }

  /// Check if payment is completed and successful
  bool get _isPaymentCompleted {
    // Payment is considered completed if:
    // 1. Payment status is explicitly 'paid', or
    // 2. paidAt timestamp exists (indicating successful payment), or
    // 3. For backwards compatibility: if status is 'scheduled' and not 'pending_payment'
    //    (this handles cases where payment_status might not be set but appointment is confirmed)

    final paymentStatus = widget.appointment.paymentStatus?.toLowerCase();
    final appointmentStatus = widget.appointment.status.toLowerCase();

    // Explicit payment completion indicators
    if (paymentStatus == 'paid' || widget.appointment.paidAt != null) {
      return true;
    }

    // Backwards compatibility: scheduled appointments that aren't pending payment
    // are considered paid (for existing data)
    if (appointmentStatus == 'scheduled' &&
        appointmentStatus != 'pending_payment') {
      return true;
    }

    return false;
  }

  /// Get the display status text
  String get _displayStatus {
    final status = widget.appointment.status.toLowerCase();

    // If status is 'scheduled' but payment is not completed, don't show 'SCHEDULED'
    if (status == 'scheduled' && !_isPaymentCompleted) {
      return ''; // Return empty string to hide the status chip
    }

    return widget.appointment.status.toUpperCase();
  }

  /// Check if status chip should be shown
  bool get _shouldShowStatusChip {
    final status = widget.appointment.status.toLowerCase();

    // Hide status chip for 'scheduled' appointments without completed payment
    if (status == 'scheduled' && !_isPaymentCompleted) {
      return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return _buildAppointmentCard();
  }

  Widget _buildAppointmentCard() {
    final dateFormat = DateFormat('EEE, MMM d');
    final formattedDate = dateFormat.format(widget.appointment.date);

    // Format time in a more elegant way
    final startTime = widget.appointment.timeSlot['start_time'] ?? '';
    final endTime = widget.appointment.timeSlot['end_time'] ?? '';

    // Determine if this is a telemedicine appointment that can be joined
    // Only allow video consultation if payment is completed for scheduled appointments
    final bool canJoinVideo = widget.appointment.isTelemedicine &&
        widget.appointment.status.toLowerCase() == 'scheduled' &&
        _isPaymentCompleted;

    // Parse the status color
    final Color statusColor = Color(int.parse(
      widget.appointment.statusColor.replaceFirst('#', '0xFF'),
    ));

    // Get theme gradient color for buttons
    const themeColor = Color(0xFF17C3B2); // Teal theme color

    // Check if we're in desktop view
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isDesktop ? 2 : 4,
        vertical: isDesktop ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isDesktop ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(isDesktop ? 15 : 10),
            blurRadius: isDesktop ? 12 : 8,
            spreadRadius: 0,
            offset: Offset(0, isDesktop ? 3 : 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.shade100,
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(isDesktop ? 16 : 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            // Top section with date, time and status
            Container(
              padding: EdgeInsets.symmetric(
                  horizontal: isDesktop ? 16 : 12,
                  vertical: isDesktop ? 14 : 10),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  left: BorderSide(
                    color: statusColor,
                    width: 3,
                  ),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Date and time column
                  Expanded(
                    child: Row(
                      children: [
                        // Date and time info
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Date with icon
                            Row(
                              children: [
                                Icon(
                                  Icons.event,
                                  size: isDesktop ? 16 : 14,
                                  color: themeColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  formattedDate,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: isDesktop ? 15 : 13,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            // Time with icon
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: isDesktop ? 16 : 14,
                                  color: themeColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '$startTime - $endTime',
                                  style: TextStyle(
                                    fontSize: isDesktop ? 14 : 12,
                                    color: Colors.black54,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        // Telemedicine indicator (moved inline)
                        if (widget.appointment.isTelemedicine) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: isDesktop ? 8 : 6,
                                vertical: isDesktop ? 3 : 2),
                            decoration: BoxDecoration(
                              color: themeColor.withAlpha(20),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.videocam,
                                  size: isDesktop ? 14 : 12,
                                  color: themeColor,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  'Video',
                                  style: TextStyle(
                                    fontSize: isDesktop ? 12 : 10,
                                    color: themeColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Status chip - only show if payment is completed for scheduled appointments
                  if (_shouldShowStatusChip)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: isDesktop ? 10 : 8,
                        vertical: isDesktop ? 4 : 3,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _displayStatus,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.w600,
                          fontSize: isDesktop ? 12 : 10,
                          letterSpacing: 0.2,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Divider
            Container(
              height: 1,
              color: Colors.grey.shade100,
            ),

            // Appointment details
            Padding(
              padding: EdgeInsets.fromLTRB(isDesktop ? 16 : 12,
                  isDesktop ? 12 : 8, isDesktop ? 16 : 12, isDesktop ? 12 : 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Provider with avatar and service in one row
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Provider avatar
                      Container(
                        width: isDesktop ? 36 : 28,
                        height: isDesktop ? 36 : 28,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Icon(
                            Icons.person,
                            size: isDesktop ? 20 : 16,
                            color: themeColor,
                          ),
                        ),
                      ),
                      SizedBox(width: isDesktop ? 12 : 8),
                      // Provider name
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.appointment.providerName,
                              style: TextStyle(
                                fontSize: isDesktop ? 15 : 13,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (widget.appointment.service != null &&
                                widget.appointment.service!['name'] != null)
                              Text(
                                widget.appointment.service!['name'],
                                style: TextStyle(
                                  fontSize: isDesktop ? 14 : 12,
                                  color: Colors.black54,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Reason section - more compact and clickable
                  if (widget.appointment.reason.isNotEmpty) ...[
                    SizedBox(height: isDesktop ? 8 : 6),
                    GestureDetector(
                      onTap: () => _showReasonDialog(),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: Colors.grey.shade200,
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.description_outlined,
                                  size: 12,
                                  color: Color(0xFFEC4899),
                                ),
                                const SizedBox(width: 4),
                                const Text(
                                  'REASON',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFFEC4899),
                                    letterSpacing: 0.5,
                                  ),
                                ),
                                const Spacer(),
                                Icon(
                                  Icons.open_in_full,
                                  size: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              widget.appointment.reason,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.black87,
                                height: 1.3,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (widget.appointment.reason.length > 100) ...[
                              const SizedBox(height: 4),
                              Text(
                                'Tap to view full details',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey.shade600,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],

                  // Action buttons
                  SizedBox(height: isDesktop ? 8 : 6),

                  // Payment button for pending payment appointments
                  if (widget.appointment.isPendingPayment &&
                      widget.appointment.amount != null)
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.only(bottom: isDesktop ? 8 : 6),
                      child: ElevatedButton.icon(
                        icon: _isProcessingAction
                            ? SizedBox(
                                width: isDesktop ? 16 : 14,
                                height: isDesktop ? 16 : 14,
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Icon(Icons.payment, size: isDesktop ? 18 : 16),
                        label: Text(
                          _isProcessingAction
                              ? 'Processing...'
                              : 'Pay ${widget.appointment.formattedAmount}',
                          style: TextStyle(
                            fontSize: isDesktop ? 15 : 13,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: themeColor,
                          padding: EdgeInsets.symmetric(
                              vertical: isDesktop ? 10 : 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                          minimumSize: Size(0, isDesktop ? 36 : 32),
                        ),
                        onPressed: _isProcessingAction ? null : _processPayment,
                      ),
                    ),

                  // Video consultation button
                  if (canJoinVideo)
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.only(bottom: isDesktop ? 8 : 6),
                      child: ElevatedButton.icon(
                        icon: Icon(Icons.videocam, size: isDesktop ? 18 : 16),
                        label: Text(
                          'Join Video Consultation',
                          style: TextStyle(
                            fontSize: isDesktop ? 15 : 13,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: themeColor,
                          padding: EdgeInsets.symmetric(
                              vertical: isDesktop ? 10 : 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                          minimumSize: Size(0, isDesktop ? 36 : 32),
                        ),
                        onPressed: _joinVideoConsultation,
                      ),
                    ),

                  // Cancel button for upcoming appointments (only for paid scheduled appointments)
                  if (widget.appointment.status != 'cancelled' &&
                      widget.appointment.status != 'completed' &&
                      widget.appointment.status != 'pending_payment' &&
                      _isPaymentCompleted &&
                      (widget.appointment.date.isAfter(DateTime.now()) ||
                          (widget.appointment.date.day == DateTime.now().day &&
                              widget.appointment.date.month ==
                                  DateTime.now().month &&
                              widget.appointment.date.year ==
                                  DateTime.now().year)))
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        icon: _isProcessingAction
                            ? SizedBox(
                                width: isDesktop ? 14 : 12,
                                height: isDesktop ? 14 : 12,
                                child: const CircularProgressIndicator(
                                  color: Colors.red,
                                  strokeWidth: 2,
                                ),
                              )
                            : Icon(Icons.cancel_outlined,
                                size: isDesktop ? 16 : 14),
                        label: Text(
                          _isProcessingAction ? '...' : 'Cancel Appointment',
                          style: TextStyle(
                            fontSize: isDesktop ? 15 : 13,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        onPressed:
                            _isProcessingAction ? null : _cancelAppointment,
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red, width: 1),
                          padding:
                              EdgeInsets.symmetric(vertical: isDesktop ? 8 : 6),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          minimumSize: Size(0, isDesktop ? 34 : 30),
                        ),
                      ),
                    ),

                  // Action buttons for cancelled, pending payment, or past appointments
                  if (widget.appointment.status == 'cancelled' ||
                      widget.appointment.status == 'pending_payment' ||
                      widget.appointment.status == 'completed' ||
                      (widget.appointment.date.isBefore(DateTime.now()) &&
                          !(widget.appointment.date.day == DateTime.now().day &&
                              widget.appointment.date.month ==
                                  DateTime.now().month &&
                              widget.appointment.date.year ==
                                  DateTime.now().year)))
                    Row(
                      children: [
                        // Reschedule button (always available except for cancelled appointments)
                        if (widget.appointment.status != 'cancelled')
                          Expanded(
                            child: OutlinedButton.icon(
                              icon: _isProcessingAction
                                  ? SizedBox(
                                      width: isDesktop ? 14 : 12,
                                      height: isDesktop ? 14 : 12,
                                      child: const CircularProgressIndicator(
                                        color: Color(0xFF17C3B2),
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : Icon(Icons.event_available,
                                      size: isDesktop ? 16 : 14),
                              label: Text(
                                _isProcessingAction ? '...' : 'Reschedule',
                                style: TextStyle(
                                  fontSize: isDesktop ? 15 : 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              onPressed: _isProcessingAction
                                  ? null
                                  : _rescheduleAppointment,
                              style: OutlinedButton.styleFrom(
                                foregroundColor: themeColor,
                                side: const BorderSide(
                                    color: Color(0xFF17C3B2), width: 1),
                                padding: EdgeInsets.symmetric(
                                    vertical: isDesktop ? 10 : 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                minimumSize: Size(0, isDesktop ? 38 : 34),
                              ),
                            ),
                          ),

                        // Add spacing if both buttons are shown
                        if (widget.appointment.status != 'cancelled')
                          const SizedBox(width: 8),

                        // Delete button
                        Expanded(
                          child: TextButton.icon(
                            icon: _isProcessingAction
                                ? const SizedBox(
                                    width: 12,
                                    height: 12,
                                    child: CircularProgressIndicator(
                                      color: Colors.red,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Icon(Icons.delete_outline, size: 14),
                            label: Text(
                              _isProcessingAction ? '...' : 'Delete',
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            onPressed:
                                _isProcessingAction ? null : _deleteAppointment,
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.red,
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              minimumSize: const Size(0, 34),
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
