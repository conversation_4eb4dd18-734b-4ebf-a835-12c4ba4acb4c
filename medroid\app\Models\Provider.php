<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Provider extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'user_id',
        'clinic_id',
        'specialization',
        'license_number',
        'verification_status',
        'verified_at',
        'rejection_reason',
        'practice_locations',
        'rating',
        'pricing',
        'gender',
        'languages',
        'bio',
        'profile_image',
        'education',
        'accepts_insurance',
        'insurance_providers',
        'weekly_availability',
        'absences',
        'certifications',
    ];

    protected $casts = [
        'practice_locations' => 'array',
        'pricing' => 'array',
        'insurance_providers' => 'array',
        'languages' => 'array',
        'weekly_availability' => 'array',
        'absences' => 'array',
        'certifications' => 'array',
        'rating' => 'decimal:1',
        'accepts_insurance' => 'boolean',
        'verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the weekly availability attribute with default values if null
     *
     * @param  mixed  $value
     * @return array
     */
    public function getWeeklyAvailabilityAttribute($value)
    {
        // Default structure with empty slots for all days
        $defaultStructure = [
            ['day' => 'Monday', 'slots' => []],
            ['day' => 'Tuesday', 'slots' => []],
            ['day' => 'Wednesday', 'slots' => []],
            ['day' => 'Thursday', 'slots' => []],
            ['day' => 'Friday', 'slots' => []],
            ['day' => 'Saturday', 'slots' => []],
            ['day' => 'Sunday', 'slots' => []],
        ];

        // If the value is null or not properly formatted, return the default structure
        if ($value === null) {
            return $defaultStructure;
        }

        // If the value is a string (JSON), try to decode it
        if (is_string($value)) {
            try {
                $value = json_decode($value, true);
            } catch (\Exception $e) {
                return $defaultStructure;
            }
        }

        // If the value is still not an array or is empty, return the default structure
        if (!is_array($value) || empty($value)) {
            return $defaultStructure;
        }

        // Ensure all days are present
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        try {
            $existingDays = array_column($value, 'day');

            foreach ($days as $day) {
                if (!in_array($day, $existingDays)) {
                    $value[] = ['day' => $day, 'slots' => []];
                }
            }
        } catch (\Exception $e) {
            return $defaultStructure;
        }

        return $value;
    }

    /**
     * Get the absences attribute with default values if null
     *
     * @param  mixed  $value
     * @return array
     */
    public function getAbsencesAttribute($value)
    {
        // If the value is null, return an empty array
        if ($value === null) {
            return [];
        }

        // If the value is a string (JSON), try to decode it
        if (is_string($value)) {
            try {
                $value = json_decode($value, true);
            } catch (\Exception $e) {
                return [];
            }
        }

        // If the value is still not an array, return an empty array
        if (!is_array($value)) {
            return [];
        }

        return $value;
    }

    /**
     * The attributes that should be visible in arrays.
     *
     * @var array
     */
    protected $visible = [
        'id',
        'user_id',
        'clinic_id',
        'specialization',
        'license_number',
        'verification_status',
        'practice_locations',
        'rating',
        'pricing',
        'gender',
        'languages',
        'bio',
        'profile_image',
        'education',
        'accepts_insurance',
        'insurance_providers',
        'created_at',
        'updated_at',
        'user', // Include the user relationship to get the provider's name
        'clinic', // Include the clinic relationship
        'availability', // Include the availability relationship
        'services', // Include the services relationship
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function services()
    {
        return $this->hasMany(Service::class);
    }

    /**
     * Get the provider's availability.
     */
    public function availability()
    {
        return $this->hasMany(ProviderAvailability::class);
    }

    /**
     * Get the provider's absences.
     */
    public function absences()
    {
        return $this->hasMany(ProviderAbsence::class);
    }

    /**
     * Get the provider's languages.
     */
    public function languages()
    {
        return $this->belongsToMany(Language::class, 'provider_languages');
    }

    /**
     * Get the default weekly availability structure
     *
     * @return array
     */
    public function getDefaultWeeklyAvailability()
    {
        return [
            ['day' => 'Monday', 'slots' => []],
            ['day' => 'Tuesday', 'slots' => []],
            ['day' => 'Wednesday', 'slots' => []],
            ['day' => 'Thursday', 'slots' => []],
            ['day' => 'Friday', 'slots' => []],
            ['day' => 'Saturday', 'slots' => []],
            ['day' => 'Sunday', 'slots' => []],
        ];
    }

    /**
     * Check if the provider is available on a specific date and time
     *
     * @param string $date Date in Y-m-d format
     * @param array $timeSlot Time slot with start_time and end_time keys
     * @param int|null $excludeAppointmentId Optional appointment ID to exclude from conflict check (for rescheduling)
     * @return bool
     */
    public function isAvailable($date, $timeSlot, $excludeAppointmentId = null)
    {
        try {
            // Convert date to Carbon instance
            $dateObj = Carbon::parse($date);
            $dayOfWeek = $dateObj->format('l'); // Monday, Tuesday, etc.

            // Check if the provider is absent on this date
            if ($this->isAbsent($dateObj)) {
                return false;
            }

            // Get availability data using our reliable method
            $weeklyAvailability = $this->getAvailabilityData();

            // Check if the provider has weekly availability for this day
            $dayAvailability = collect($weeklyAvailability)
                ->firstWhere('day', $dayOfWeek);

            if (!$dayAvailability) {
                return false;
            }

            // Ensure slots exist and are properly formatted
            if (!isset($dayAvailability['slots']) || !is_array($dayAvailability['slots'])) {
                return false;
            }

            // Check if the requested time slot fits within the provider's available slots
            $requestStart = strtotime($timeSlot['start_time']);
            $requestEnd = strtotime($timeSlot['end_time']);

            foreach ($dayAvailability['slots'] as $slot) {
                // Skip invalid slots
                if (!isset($slot['start_time']) || !isset($slot['end_time'])) {
                    continue;
                }

                $slotStart = strtotime($slot['start_time']);
                $slotEnd = strtotime($slot['end_time']);

                // Check if the requested slot is within this available slot
                if ($requestStart >= $slotStart && $requestEnd <= $slotEnd) {
                    // Now check if there are any existing appointments that conflict
                    // A conflict occurs when the requested slot overlaps with an existing appointment
                    // This happens when:
                    // 1. The requested start time is before the existing appointment's end time, AND
                    // 2. The requested end time is after the existing appointment's start time
                    // We check ALL appointments regardless of service to prevent double-booking
                    // Only scheduled/confirmed appointments should block the slot
                    $conflictingAppointments = $this->appointments()
                        ->where('date', $date)
                        ->whereIn('status', ['scheduled', 'confirmed']);

                    // If we're rescheduling, exclude the current appointment from the conflict check
                    if ($excludeAppointmentId) {
                        $conflictingAppointments = $conflictingAppointments->where('id', '!=', $excludeAppointmentId);
                    }

                    $conflictingAppointments = $conflictingAppointments->where(function ($query) use ($requestStart, $requestEnd) {
                            // Check for overlapping appointments:
                            // 1. Appointment start time < Request end time AND
                            // 2. Appointment end time > Request start time
                            $query->whereRaw("(JSON_EXTRACT(time_slot, '$.start_time') < ? AND JSON_EXTRACT(time_slot, '$.end_time') > ?)",
                                [date('H:i', $requestEnd), date('H:i', $requestStart)]);
                        })
                        ->whereIn('status', ['scheduled', 'confirmed'])
                        ->get();

                    return $conflictingAppointments->count() === 0;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get the absences data directly, bypassing potential issues
     *
     * @return array
     */
    public function getAbsencesData()
    {
        try {
            // First try to get the data from the absences attribute
            if (!empty($this->absences)) {
                return $this->absences;
            }

            // If that fails, try to get it directly from the database
            $rawData = \DB::table('providers')
                ->where('id', $this->id)
                ->select('absences')
                ->first();

            if ($rawData && !empty($rawData->absences)) {
                $absences = json_decode($rawData->absences, true);
                if (is_array($absences)) {
                    return $absences;
                }
            }

            // If all else fails, return an empty array
            return [];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Check if the provider is absent on a specific date
     *
     * @param Carbon $date
     * @return bool
     */
    public function isAbsent($date)
    {
        try {
            // Get absences data using our reliable method
            $absences = $this->getAbsencesData();

            if (empty($absences)) {
                return false;
            }

            foreach ($absences as $absence) {
                // Skip invalid absences
                if (!isset($absence['start_date']) || !isset($absence['end_date'])) {
                    continue;
                }

                $startDate = Carbon::parse($absence['start_date']);
                $endDate = Carbon::parse($absence['end_date']);

                // Check for exceptions to this absence period
                if (isset($absence['exceptions']) && is_array($absence['exceptions'])) {
                    foreach ($absence['exceptions'] as $exception) {
                        $exceptionDate = Carbon::parse($exception);
                        if ($date->isSameDay($exceptionDate)) {
                            // This date is an exception to the absence, so return false
                            return false;
                        }
                    }
                }

                // Check if the date falls within an absence period
                if ($date->between($startDate, $endDate)) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get available time slots for a specific date
     *
     * @param string $date Date in Y-m-d format
     * @return array
     */
    public function getAvailabilityData()
    {
        try {
            // Always get fresh data from the database to avoid caching issues
            $rawData = \DB::table('providers')
                ->where('id', $this->id)
                ->select('weekly_availability')
                ->first();

            if ($rawData && !empty($rawData->weekly_availability)) {
                $weeklyAvailability = json_decode($rawData->weekly_availability, true);
                if (is_array($weeklyAvailability) && !empty($weeklyAvailability)) {
                    // Validate the structure of the availability data
                    $validatedAvailability = [];
                    foreach ($weeklyAvailability as $dayData) {
                        if (isset($dayData['day']) && isset($dayData['slots']) && is_array($dayData['slots'])) {
                            // Only include days that have actual time slots
                            if (!empty($dayData['slots'])) {
                                $validatedAvailability[] = $dayData;
                            }
                        }
                    }

                    if (!empty($validatedAvailability)) {
                        \Log::info('Provider availability data retrieved', [
                            'provider_id' => $this->id,
                            'days_with_availability' => count($validatedAvailability)
                        ]);
                        return $validatedAvailability;
                    }
                }
            }

            // If no valid weekly availability is found, log it
            \Log::warning('No valid weekly availability found for provider', [
                'provider_id' => $this->id,
                'provider_name' => $this->user->name ?? 'Unknown',
                'raw_data' => $rawData ? $rawData->weekly_availability : null
            ]);

            // Return empty array - provider needs to set up availability
            return [];
        } catch (\Exception $e) {
            \Log::error('Error getting provider availability data', [
                'provider_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Return empty array on error
            return [];
        }
    }

    /**
     * Get available time slots for a specific date
     *
     * @param string $date Date in Y-m-d format
     * @param int|null $serviceId Optional service ID to get service-specific availability
     * @return array
     */
    public function getAvailableTimeSlots($date, $serviceId = null)
    {
        try {
            // Convert date to Carbon instance
            $dateObj = Carbon::parse($date);
            $dayOfWeek = $dateObj->format('l'); // Monday, Tuesday, etc.

            // Check if the provider is absent on this date
            if ($this->isAbsent($dateObj)) {
                return [];
            }

            // If a service ID is provided, check for service-specific availability first
            $serviceAvailability = null;
            if ($serviceId) {
                $service = $this->services()->find($serviceId);
                if ($service && !empty($service->availability)) {
                    $serviceAvailability = collect($service->availability)
                        ->firstWhere('day', $dayOfWeek);
                }
            }

            // Determine which availability to use (service-specific or provider general)
            if ($serviceAvailability && isset($serviceAvailability['slots']) && is_array($serviceAvailability['slots'])) {
                $daySlots = $serviceAvailability['slots'];
            } else {
                // Otherwise, use provider's general availability

                // Get availability data using our reliable method
                $weeklyAvailability = $this->getAvailabilityData();

                // Check if weekly_availability is null or empty
                if (empty($weeklyAvailability)) {
                    return [];
                }

                // Get the provider's weekly availability for this day
                $dayAvailability = collect($weeklyAvailability)
                    ->firstWhere('day', $dayOfWeek);

                if (!$dayAvailability) {
                    return [];
                }

                // Ensure slots exist and are properly formatted
                if (!isset($dayAvailability['slots']) || !is_array($dayAvailability['slots'])) {
                    return [];
                }

                $daySlots = $dayAvailability['slots'];
            }

            // Get ALL appointments for this date, regardless of service
            // This ensures we don't double-book the provider
            // Only scheduled/confirmed appointments should block the slot
            $appointments = $this->appointments()
                ->where('date', $date)
                ->whereIn('status', ['scheduled', 'confirmed'])
                ->get();

            $availableSlots = [];

            // For each available slot in the day's schedule
            foreach ($daySlots as $slot) {
                // Skip invalid slots
                if (!isset($slot['start_time']) || !isset($slot['end_time'])) {
                    continue;
                }

                $slotStart = strtotime($slot['start_time']);
                $slotEnd = strtotime($slot['end_time']);

                // Get service duration or default to 15 minutes
                $slotDuration = 15 * 60; // 15 minutes in seconds (default)

                // If service ID is provided, use service duration instead
                if ($serviceId) {
                    $service = $this->services()->find($serviceId);
                    if ($service) {
                        $slotDuration = $service->duration * 60; // Convert minutes to seconds
                    }
                } else {
                    // If no service ID, try to get the first service's duration
                    $firstService = $this->services()->first();
                    if ($firstService) {
                        $slotDuration = $firstService->duration * 60; // Convert minutes to seconds
                    }
                }

                // Get current time
                $now = Carbon::now();
                $currentTime = $now->timestamp;
                $isToday = $dateObj->isSameDay($now);

                // Generate potential appointment slots
                for ($time = $slotStart; $time + $slotDuration <= $slotEnd; $time += $slotDuration) {
                    // Skip slots that are in the past if the date is today
                    if ($isToday && $time <= $currentTime) {
                        continue;
                    }

                    $potentialSlot = [
                        'start_time' => date('H:i', $time),
                        'end_time' => date('H:i', $time + $slotDuration)
                    ];

                    // Check if this slot conflicts with ANY existing appointment
                    // regardless of which service it's for
                    $isConflicting = false;
                    $potentialSlotStart = $time;
                    $potentialSlotEnd = $time + $slotDuration;

                    foreach ($appointments as $appointment) {
                        try {
                            // Handle different time slot formats
                            $timeSlot = $appointment->time_slot;
                            $startTime = null;
                            $endTime = null;

                            if (isset($timeSlot['start_time'])) {
                                $startTime = $timeSlot['start_time'];
                                $endTime = $timeSlot['end_time'];
                            } elseif (isset($timeSlot['start'])) {
                                $startTime = $timeSlot['start'];
                                $endTime = $timeSlot['end'];
                            }

                            if (!$startTime || !$endTime) {
                                continue;
                            }

                            $apptStart = strtotime($startTime);
                            $apptEnd = strtotime($endTime);

                            // A conflict occurs when the potential slot overlaps with an existing appointment
                            // This happens when:
                            // 1. The potential slot start time is before the existing appointment's end time, AND
                            // 2. The potential slot end time is after the existing appointment's start time
                            if ($potentialSlotStart < $apptEnd && $potentialSlotEnd > $apptStart) {
                                $isConflicting = true;
                                break;
                            }
                        } catch (\Exception $e) {
                            continue;
                        }
                    }

                    if (!$isConflicting) {
                        $availableSlots[] = $potentialSlot;
                    }
                }
            }

            return $availableSlots;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Scope to filter providers by clinic.
     */
    public function scopeForClinic($query, $clinicId)
    {
        return $query->where('clinic_id', $clinicId);
    }

    /**
     * Scope to get providers without a clinic.
     */
    public function scopeWithoutClinic($query)
    {
        return $query->whereNull('clinic_id');
    }

    /**
     * Scope to get verified providers.
     */
    public function scopeVerified($query)
    {
        return $query->where('verification_status', 'verified');
    }

    /**
     * Scope to get active providers (with active users).
     */
    public function scopeActive($query)
    {
        return $query->whereHas('user', function($q) {
            $q->where('is_active', true);
        });
    }
}